#!/bin/bash

# 最终验证测试脚本
# 验证所有SN号系统功能正常工作
# 使用方法: ./test_final_verification.sh [server_url]

SERVER_URL=${1:-"http://localhost:5678"}
API_BASE="$SERVER_URL/api"
FACTORY_API="$API_BASE/factory"

# 工厂永久Token
FACTORY_TOKEN="FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"

# 测试用户ID
TEST_USER_ID="022b605dc3421000"

echo "🎯 SN号系统最终验证测试"
echo "服务器: $SERVER_URL"
echo "测试用户: $TEST_USER_ID"
echo "=================================================="

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0

# 测试函数
test_case() {
    local test_name="$1"
    local expected_result="$2"
    local actual_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [[ "$actual_result" == "$expected_result" ]]; then
        echo "✅ $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo "❌ $test_name (期望: $expected_result, 实际: $actual_result)"
    fi
}

# 1. 工厂API健康检查
echo "1. 🏭 工厂API测试..."
FACTORY_HEALTH=$(curl -s -X GET "$FACTORY_API/health" | jq -r '.status')
test_case "工厂健康检查" "ok" "$FACTORY_HEALTH"

# 2. 申请复合SN号
PCB_SN="PCB_FINAL_TEST_$(date +%s)"
APPLY_RESULT=$(curl -s -X POST "$FACTORY_API/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $FACTORY_TOKEN" \
  -d "{
    \"pcb_sn\": \"$PCB_SN\",
    \"device_type\": \"toilet_v1\",
    \"quantity\": 1,
    \"remark\": \"最终验证测试\"
  }")

APPLY_SUCCESS=$(echo "$APPLY_RESULT" | jq -r '.success')
test_case "复合SN号申请" "true" "$APPLY_SUCCESS"

if [[ "$APPLY_SUCCESS" == "true" ]]; then
    COMPOSITE_SN=$(echo "$APPLY_RESULT" | jq -r '.data.sn_list[0]')
    echo "   生成的SN号: $COMPOSITE_SN"
    
    # 验证SN号格式
    if [[ "$COMPOSITE_SN" =~ ^[A-F0-9]{8}_.*_[0-9]{13,}$ ]]; then
        test_case "复合SN号格式验证" "true" "true"
    else
        test_case "复合SN号格式验证" "true" "false"
    fi
else
    COMPOSITE_SN=""
    test_case "复合SN号格式验证" "true" "false"
fi

# 3. 硬件关联测试
echo ""
echo "2. 🔧 硬件关联测试..."

if [[ -n "$COMPOSITE_SN" ]]; then
    # 添加复合格式硬件关联
    HARDWARE_ADD_RESULT=$(curl -s -X POST "$API_BASE/devices" \
      -H "Content-Type: application/json" \
      -d "{
        \"user_id\": \"$TEST_USER_ID\",
        \"hardware_sn\": \"$COMPOSITE_SN\",
        \"remark\": \"最终验证测试\"
      }")
    
    if echo "$HARDWARE_ADD_RESULT" | jq -e '.id' > /dev/null; then
        test_case "复合SN硬件关联添加" "true" "true"
        
        # 查询硬件关联
        HARDWARE_QUERY=$(curl -s -X GET "$API_BASE/devices/hardware/$COMPOSITE_SN")
        QUERY_USER_ID=$(echo "$HARDWARE_QUERY" | jq -r '.user_id')
        test_case "复合SN硬件关联查询" "$TEST_USER_ID" "$QUERY_USER_ID"
        
        # 检查关联存在
        CHECK_RESULT=$(curl -s -X GET "$API_BASE/devices/check?user_id=$TEST_USER_ID&hardware_sn=$COMPOSITE_SN")
        CHECK_EXISTS=$(echo "$CHECK_RESULT" | jq -r '.exists')
        test_case "复合SN关联存在检查" "true" "$CHECK_EXISTS"
    else
        test_case "复合SN硬件关联添加" "true" "false"
        test_case "复合SN硬件关联查询" "$TEST_USER_ID" "failed"
        test_case "复合SN关联存在检查" "true" "failed"
    fi
else
    test_case "复合SN硬件关联添加" "true" "skipped"
    test_case "复合SN硬件关联查询" "$TEST_USER_ID" "skipped"
    test_case "复合SN关联存在检查" "true" "skipped"
fi

# 4. 旧格式兼容性测试
echo ""
echo "3. 🔄 旧格式兼容性测试..."

LEGACY_SN="ABCD1234"
LEGACY_ADD_RESULT=$(curl -s -X POST "$API_BASE/devices" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"hardware_sn\": \"$LEGACY_SN\",
    \"remark\": \"旧格式兼容性测试\"
  }")

if echo "$LEGACY_ADD_RESULT" | jq -e '.id' > /dev/null; then
    test_case "旧格式SN硬件关联添加" "true" "true"
    
    # 查询旧格式硬件关联
    LEGACY_QUERY=$(curl -s -X GET "$API_BASE/devices/hardware/$LEGACY_SN")
    LEGACY_QUERY_USER_ID=$(echo "$LEGACY_QUERY" | jq -r '.user_id')
    test_case "旧格式SN硬件关联查询" "$TEST_USER_ID" "$LEGACY_QUERY_USER_ID"
else
    test_case "旧格式SN硬件关联添加" "true" "false"
    test_case "旧格式SN硬件关联查询" "$TEST_USER_ID" "failed"
fi

# 5. 工厂SN状态管理测试
echo ""
echo "4. 📊 工厂SN状态管理测试..."

if [[ -n "$COMPOSITE_SN" ]]; then
    # 检查SN存在
    SN_CHECK_RESULT=$(curl -s -X GET "$FACTORY_API/sn/$COMPOSITE_SN/check" \
      -H "Authorization: Bearer $FACTORY_TOKEN")
    SN_EXISTS=$(echo "$SN_CHECK_RESULT" | jq -r '.data.exists')
    test_case "工厂SN存在检查" "true" "$SN_EXISTS"
    
    # 更新SN状态
    SN_UPDATE_RESULT=$(curl -s -X PUT "$FACTORY_API/sn/update" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $FACTORY_TOKEN" \
      -d "{
        \"sn\": \"$COMPOSITE_SN\",
        \"status\": 2,
        \"operator\": \"最终验证测试员\",
        \"remark\": \"最终验证测试使用\"
      }")
    
    UPDATE_SUCCESS=$(echo "$SN_UPDATE_RESULT" | jq -r '.success')
    test_case "工厂SN状态更新" "true" "$UPDATE_SUCCESS"
    
    # 按PCB SN查询
    PCB_QUERY_RESULT=$(curl -s -X GET "$FACTORY_API/sn/query?pcb_sn=$PCB_SN&page=1&page_size=5" \
      -H "Authorization: Bearer $FACTORY_TOKEN")
    PCB_QUERY_SUCCESS=$(echo "$PCB_QUERY_RESULT" | jq -r '.success')
    test_case "按PCB SN查询" "true" "$PCB_QUERY_SUCCESS"
else
    test_case "工厂SN存在检查" "true" "skipped"
    test_case "工厂SN状态更新" "true" "skipped"
    test_case "按PCB SN查询" "true" "skipped"
fi

# 6. 统计信息测试
echo ""
echo "5. 📈 统计信息测试..."

STATS_RESULT=$(curl -s -X GET "$FACTORY_API/statistics" \
  -H "Authorization: Bearer $FACTORY_TOKEN")
STATS_SUCCESS=$(echo "$STATS_RESULT" | jq -r '.success')
test_case "统计信息获取" "true" "$STATS_SUCCESS"

if [[ "$STATS_SUCCESS" == "true" ]]; then
    TOTAL_COUNT=$(echo "$STATS_RESULT" | jq -r '.data.total_count')
    if [[ "$TOTAL_COUNT" =~ ^[0-9]+$ ]] && [[ "$TOTAL_COUNT" -gt 0 ]]; then
        test_case "统计数据有效性" "true" "true"
        echo "   总SN数量: $TOTAL_COUNT"
    else
        test_case "统计数据有效性" "true" "false"
    fi
else
    test_case "统计数据有效性" "true" "false"
fi

# 7. Token认证测试
echo ""
echo "6. 🔐 Token认证测试..."

# 无Token访问（应该失败）
NO_TOKEN_RESULT=$(curl -s -X POST "$FACTORY_API/sn/apply" \
  -H "Content-Type: application/json" \
  -d '{"pcb_sn": "test", "quantity": 1}')

if echo "$NO_TOKEN_RESULT" | grep -q "Authorization header required"; then
    test_case "无Token访问拒绝" "true" "true"
else
    test_case "无Token访问拒绝" "true" "false"
fi

# 错误Token访问（应该失败）
WRONG_TOKEN_RESULT=$(curl -s -X POST "$FACTORY_API/sn/apply" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer WRONG_TOKEN" \
  -d '{"pcb_sn": "test", "quantity": 1}')

if echo "$WRONG_TOKEN_RESULT" | grep -q "Invalid factory token"; then
    test_case "错误Token访问拒绝" "true" "true"
else
    test_case "错误Token访问拒绝" "true" "false"
fi

# 最终结果
echo ""
echo "=================================================="
echo "🎯 最终验证测试完成!"
echo ""
echo "📊 测试结果统计:"
echo "   总测试数: $TOTAL_TESTS"
echo "   通过测试: $PASSED_TESTS"
echo "   失败测试: $((TOTAL_TESTS - PASSED_TESTS))"
echo "   成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
echo ""

if [[ $PASSED_TESTS -eq $TOTAL_TESTS ]]; then
    echo "🎉 所有测试通过！SN号系统运行正常！"
    echo ""
    echo "✅ 验证通过的功能:"
    echo "   - 工厂API健康检查"
    echo "   - 复合SN号生成和验证"
    echo "   - 硬件关联管理（新旧格式兼容）"
    echo "   - SN状态管理"
    echo "   - 统计信息获取"
    echo "   - Token认证安全"
    echo ""
    echo "🔑 永久Token: FACTORY_SN_TOKEN_2025_PERMANENT_ACCESS_KEY_8F3A9B2C7E1D6H4K"
    echo "📦 测试PCB SN: $PCB_SN"
    if [[ -n "$COMPOSITE_SN" ]]; then
        echo "🏷️  生成的复合SN: $COMPOSITE_SN"
    fi
    exit 0
else
    echo "⚠️  部分测试失败，请检查系统配置"
    exit 1
fi

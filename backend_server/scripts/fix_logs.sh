#!/bin/bash

# 脚本用于批量替换日志语句
# 将 log.Printf 和 fmt.Printf 替换为统一的 logger 调用

TARGET_FILE="backend_server/pkg/cattoilet/service.go"

echo "开始替换 $TARGET_FILE 中的日志语句..."

# 替换 log.Printf 为 logger.Errorf (错误相关)
sed -i 's/log\.Printf("Failed to \([^"]*\): %v", \([^)]*\))/logger.Errorf("Failed to \1: %v", \2)/g' "$TARGET_FILE"

# 替换 log.Printf 为 logger.Warnf (警告相关)
sed -i 's/log\.Printf("Warning: \([^"]*\): %v", \([^)]*\))/logger.Warnf("\1: %v", \2)/g' "$TARGET_FILE"

# 替换 log.Printf 为 logger.Infof (信息相关)
sed -i 's/log\.Printf("Delete video file: %s", \([^)]*\))/logger.Infof("Delete video file: %s", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Deleted video file: %s", \([^)]*\))/logger.Infof("Deleted video file: %s", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Device %s firmware version updated to %s", \([^)]*\))/logger.Infof("Device %s firmware version updated to %s", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Found cat name: %s for animal_id: %s", \([^)]*\))/logger.Infof("Found cat name: %s for animal_id: %s", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Using default cat name, animal_id: %s", \([^)]*\))/logger.Infof("Using default cat name, animal_id: %s", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Notification sent: %v", \([^)]*\))/logger.Infof("Notification sent: %v", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Checking status for %d devices", \([^)]*\))/logger.Infof("Checking status for %d devices", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Calculating statistics for %d devices", \([^)]*\))/logger.Infof("Calculating statistics for %d devices", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Device %s status updated to %v", \([^)]*\))/logger.Infof("Device %s status updated to %v", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Updated statistics for device %s: online_rate=%d%%, signal_avg=%d, storage=%d%%", \([^)]*\))/logger.Infof("Updated statistics for device %s: online_rate=%d%%, signal_avg=%d, storage=%d%%", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Cleaned %d device status history records older than %v", \([^)]*\))/logger.Infof("Cleaned %d device status history records older than %v", \1)/g' "$TARGET_FILE"

# 替换 log.Printf 为 logger.Debugf (调试相关)
sed -i 's/log\.Printf("Setting AI service timeout to %d minutes", \([^)]*\))/logger.Debugf("Setting AI service timeout to %d minutes", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Sending request to AI service, attempt %d\/%d, Content-Length: %d", \([^)]*\))/logger.Debugf("Sending request to AI service, attempt %d\/%d, Content-Length: %d", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Retrying in %v\.\.\.", \([^)]*\))/logger.Debugf("Retrying in %v...", \1)/g' "$TARGET_FILE"

# 替换特殊的日志语句
sed -i 's/log\.Printf("重置超时OTA状态失败: %v", \([^)]*\))/logger.Errorf("重置超时OTA状态失败: %v", \1)/g' "$TARGET_FILE"
sed -i 's/log\.Printf("Device status monitor started with settings: heartbeat=%dm, timeout=%dm, stats_window=%dh", \([^)]*\))/logger.Infof("Device status monitor started with settings: heartbeat=%dm, timeout=%dm, stats_window=%dh", \1)/g' "$TARGET_FILE"

echo "日志语句替换完成！"
